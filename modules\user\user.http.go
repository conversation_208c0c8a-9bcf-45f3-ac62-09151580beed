package user

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewUserHTTP(e *echo.Echo) {
	user := &UserController{}
	e.GET("/users", core.WithHTTPContext(user.Pagination), middleware.AuthMiddleware())
	e.GET("/users/:id", core.WithHTTPContext(user.Find), middleware.AuthMiddleware())
	e.POST("/users", core.WithHTTPContext(user.Create), middleware.AuthMiddleware())
	e.PUT("/users/:id", core.WithHTTPContext(user.Update), middleware.AuthMiddleware())
	e.DELETE("/users/:id", core.WithHTTPContext(user.Delete), middleware.AuthMiddleware())
}
