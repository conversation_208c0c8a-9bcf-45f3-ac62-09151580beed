package user

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/requests"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type UserController struct {
}

func (m UserController) Pagination(c core.IHTTPContext) error {
	userSvc := services.NewUserService(c)
	res, ierr := userSvc.Pagination(c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m UserController) Find(c core.IHTTPContext) error {
	userSvc := services.NewUserService(c)
	user, err := userSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSO<PERSON>())
	}

	return c.JSON(http.StatusOK, user)
}

func (m UserController) Create(c core.IHTTPContext) error {
	input := &requests.UserCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	userSvc := services.NewUserService(c)
	payload := &services.UserCreatePayload{}
	_ = utils.Copy(payload, input)
	user, err := userSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, user)
}

func (m UserController) Update(c core.IHTTPContext) error {
	input := &requests.UserUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	userSvc := services.NewUserService(c)
	payload := &services.UserUpdatePayload{}
	_ = utils.Copy(payload, input)
	user, err := userSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, user)
}

func (m UserController) Delete(c core.IHTTPContext) error {
	userSvc := services.NewUserService(c)
	err := userSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
