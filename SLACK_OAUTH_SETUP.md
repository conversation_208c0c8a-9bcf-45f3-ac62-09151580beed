# Slack OAuth Integration Setup

This document explains how to set up and use the Slack OAuth integration for user authentication.

## Overview

The Slack callback implementation allows users to authenticate using their Slack accounts. When a user successfully authenticates with <PERSON><PERSON>ck, the system will:

1. Exchange the authorization code for an access token
2. Retrieve user information from Slack
3. Find or create a user account based on the Slack email
4. Generate an authentication token for the application
5. Return the user data and token

## Environment Configuration

Add the following environment variables to your `.env` file:

```env
SLACK_CLIENT_ID=your_slack_client_id
SLACK_CLIENT_SECRET=your_slack_client_secret
SLACK_REDIRECT_URL=https://yourdomain.com/auth/slack-callback
```

## Slack App Setup

1. Go to [Slack API](https://api.slack.com/apps) and create a new app
2. In "OAuth & Permissions", add the following scopes:
   - `openid` - Required for OpenID Connect
   - `profile` - Access to user's profile information
   - `email` - Access to user's email address
3. Set the redirect URL to match your `SLACK_REDIRECT_URL` environment variable
4. Copy the Client ID and Client Secret to your environment variables

## API Endpoint

### GET /auth/slack-callback

This endpoint handles the OAuth callback from Slack.

**Query Parameters:**
- `code` (required): Authorization code from Slack
- `state` (optional): State parameter for CSRF protection
- `error` (optional): Error code if authorization failed

**Success Response (200 OK):**
```json
{
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "display_name": "John",
    "avatar_url": "https://avatars.slack-edge.com/...",
    "role": "USER",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "token": "authentication-token"
}
```

**Error Responses:**

- `400 Bad Request` - Missing authorization code or Slack OAuth error
- `500 Internal Server Error` - Configuration missing or internal error

## Usage Flow

1. **Initiate OAuth**: Direct users to Slack's OAuth URL:
   ```
   https://slack.com/oauth/v2/authorize?client_id=YOUR_CLIENT_ID&scope=openid,profile,email&redirect_uri=YOUR_REDIRECT_URL
   ```

2. **Handle Callback**: Slack redirects to your callback URL with the authorization code

3. **Process Authentication**: The callback endpoint processes the code and returns user data + token

4. **Store Token**: Use the returned token for subsequent API calls

## Implementation Details

### Custom Token Exchange

The implementation uses a custom token exchange method instead of the standard OAuth2 library because Slack's OAuth v2 API requires `application/x-www-form-urlencoded` content type, which the standard library doesn't handle correctly for Slack.

### User Creation

When a new user authenticates via Slack:
- A new user account is created with the Slack email
- A random password is generated (user won't use it for Slack login)
- User profile is populated with Slack data (name, avatar)
- Default role is set to "USER"

### Existing User Updates

When an existing user authenticates via Slack:
- Avatar URL is updated if it has changed
- Other profile data remains unchanged

## Security Considerations

1. **Environment Variables**: Keep Slack credentials secure and never commit them to version control
2. **HTTPS**: Always use HTTPS for the redirect URL in production
3. **State Parameter**: Consider implementing CSRF protection using the state parameter
4. **Token Storage**: Store authentication tokens securely on the client side

## Testing

You can test the integration by:

1. Setting up a Slack app with the correct redirect URL
2. Configuring environment variables
3. Starting the application
4. Navigating to the Slack OAuth URL
5. Completing the OAuth flow

The callback should return user data and an authentication token that can be used for subsequent API calls.
