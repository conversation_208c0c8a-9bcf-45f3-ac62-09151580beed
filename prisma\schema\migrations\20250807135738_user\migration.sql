/*
  Warnings:

  - You are about to drop the column `team` on the `users` table. All the data in the column will be lost.

*/
-- DropIndex
DROP INDEX "public"."users_email_team_role_idx";

-- AlterTable
ALTER TABLE "public"."users" DROP COLUMN "team",
ADD COLUMN     "team_code" TEXT;

-- CreateTable
CREATE TABLE "public"."holidays" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "is_national" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "holidays_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."teams" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "start_working_at" TIME,
    "end_working_at" TIME,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "teams_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "holidays_name_date_idx" ON "public"."holidays"("name", "date");

-- CreateIndex
CREATE UNIQUE INDEX "teams_name_key" ON "public"."teams"("name");

-- CreateIndex
CREATE UNIQUE INDEX "teams_code_key" ON "public"."teams"("code");

-- CreateIndex
CREATE INDEX "teams_name_code_idx" ON "public"."teams"("name", "code");

-- CreateIndex
CREATE INDEX "users_email_team_code_role_idx" ON "public"."users"("email", "team_code", "role");

-- AddForeignKey
ALTER TABLE "public"."users" ADD CONSTRAINT "users_team_code_fkey" FOREIGN KEY ("team_code") REFERENCES "public"."teams"("code") ON DELETE SET NULL ON UPDATE CASCADE;
